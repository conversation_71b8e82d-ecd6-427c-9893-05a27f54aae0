"use client";

import { ReactNode, useEffect, useState } from 'react';
import { useAuth } from '@/lib/auth';
import AccessRestrictedAlert from './AccessRestrictedAlert';

interface SimpleAccessCheckProps {
  database: string;
  children: ReactNode;
}

/**
 * 动态权限检查组件
 * 从数据库配置中读取权限要求，而不是硬编码
 */
export default function SimpleAccessCheck({ database, children }: SimpleAccessCheckProps) {
  const { user } = useAuth();
  const [accessLevel, setAccessLevel] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // 动态获取数据库访问级别
  useEffect(() => {
    const fetchAccessLevel = async () => {
      try {
        const response = await fetch('/api/config/databases');
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data[database]) {
            const dbConfig = result.data[database];
            setAccessLevel(dbConfig.accessLevel || 'premium');
            if (process.env.NODE_ENV === 'development') {
              console.log(`🔍 [Dynamic Permission] ${database} requires: ${dbConfig.accessLevel || 'premium'}`);
            }
          } else {
            // 数据库不存在，默认需要premium权限
            setAccessLevel('premium');
            if (process.env.NODE_ENV === 'development') {
              console.log(`⚠️ [Dynamic Permission] ${database} not found in config, defaulting to premium`);
            }
          }
        } else {
          // API失败，默认允许访问（避免阻塞）
          setAccessLevel('free');
          if (process.env.NODE_ENV === 'development') {
            console.log(`⚠️ [Dynamic Permission] API failed for ${database}, defaulting to free access`);
          }
        }
      } catch (__error) {
        // 网络错误，默认允许访问
        setAccessLevel('free');
        if (process.env.NODE_ENV === 'development') {
          console.log(`⚠️ [Dynamic Permission] Network error for ${database}, defaulting to free access:`, error);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchAccessLevel();
  }, [database]);

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-500">Checking permissions...</div>
      </div>
    );
  }

  // 免费数据库：所有人都可以访问（包括未登录用户）
  if (accessLevel === 'free') {
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ [Dynamic Permission] Free database, access allowed:', database);
    }
    return <>{children}</>;
  }

  // 需要高级权限的数据库
  if (accessLevel ==="premium" || accessLevel === 'enterprise') {
    // 未登录用户
    if (!user) {
      return (
        <AccessRestrictedAlert
          databaseCode={database}
          requiredLevel={accessLevel as 'free' | 'premium' | 'enterprise'}
        />
      );
    }

    // 检查会员级别
    const userLevel = user.membershipType;
    const hasAccess =
      (accessLevel ==="premium" && (userLevel ==="premium" || userLevel === 'enterprise')) ||
      (accessLevel ==="enterprise" && userLevel === 'enterprise');

    if (hasAccess) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ [Dynamic Permission] User has ${userLevel} access for ${database} (requires ${accessLevel})`);
      }
      return <>{children}</>;
    }

    // 权限不足
    return (
      <AccessRestrictedAlert
        databaseCode={database}
        requiredLevel={accessLevel as 'free' | 'premium' | 'enterprise'}
      />
    );
  }

  // 默认情况：允许访问（对于未知访问级别）
  if (process.env.NODE_ENV === 'development') {
    console.log('✅ [Dynamic Permission] Unknown access level, allowing access:', database, accessLevel);
  }
  return <>{children}</>;
}