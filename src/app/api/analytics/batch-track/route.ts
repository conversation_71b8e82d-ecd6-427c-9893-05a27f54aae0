import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { getSession } from '@/lib/session';

// 获取客户端IP地址
function getClientIP(_request: NextRequest): string {
  const forwarded = _request.headers.get('x-forwarded-for');
  const realIP = _request.headers.get('x-real-ip');
  const remoteAddr = _request.headers.get('remote-addr');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP.trim();
  }
  if (remoteAddr) {
    return remoteAddr.trim();
  }
  return '127.0.0.1';
}

// 从路径中提取数据库信息
function extractDatabaseFromPath(path: string): string | null {
  const matches = path.match(/\/data\/list\/([^\/\?]+)/);
  return matches ? matches[1] : null;
}

// 定义事件数据类型
interface AnalyticsEvent {
  event: string;
  data?: Record<string, unknown>;
  timestamp?: number;
  sessionId?: string;
  userId?: string;
}

interface EventData {
  url?: string;
  referrer?: string;
  user_agent?: string;
  database?: string;
  viewport?: { width: number; height: number };
  screen?: { width: number; height: number };
  [key: string]: unknown;
}

interface ActivityLogData {
  userId?: string | null;
  ip: string;
  userAgent?: string;
  path: string;
  method: string;
  queryParams?: string;
  referer?: string;
  database?: string;
  eventType: string;
  sessionId?: string | null;
  createdAt: Date;
}

// 验证事件数据
function validateEvent(event: unknown): event is AnalyticsEvent {
  return (
    typeof event === 'object' &&
    event !== null &&
    'event' in event &&
    typeof (event as AnalyticsEvent).event === 'string' &&
    (event as AnalyticsEvent).event.length > 0 &&
    (event as AnalyticsEvent).event.length <= 50
  );
}

// 数据截断和验证函数
function truncateString(str: string | null | undefined, maxLength: number): string | undefined {
  if (!str) return undefined;
  return str.length > maxLength ? str.substring(0, maxLength - 3) + '...' : str;
}

function sanitizeData(data: Partial<ActivityLogData>): ActivityLogData {
  return {
    userId: data.userId || null,
    ip: truncateString(data.ip, 50) || '127.0.0.1',
    userAgent: truncateString(data.userAgent, 500),
    path: truncateString(data.path, 255) || '/',
    method: truncateString(data.method, 10) || 'UNKNOWN',
    queryParams: truncateString(data.queryParams, 1000),
    referer: truncateString(data.referer, 255),
    database: truncateString(data.database, 50),
    eventType: truncateString(data.eventType, 50) || 'unknown',
    sessionId: truncateString(data.sessionId, 100),
    createdAt: data.createdAt || new Date(),
  };
}

// 处理单个事件
async function processEvent(
  event: unknown,
  defaultData: {
    userId?: string | null;
    ip: string;
    userAgent?: string;
    referer?: string;
    sessionId?: string | null;
  }
): Promise<ActivityLogData | null> {
  if (!validateEvent(event)) {
    return null;
  }

  const {
    event: eventType,
    data = {},
    timestamp,
    sessionId,
    userId: eventUserId,
  } = event;

  // 从数据中提取信息
  const eventData = data as EventData;
  const {
    url,
    referrer,
    user_agent,
    database: explicitDatabase,
    viewport,
    screen,
    ...otherEventData
  } = eventData;

  // 尝试从URL或显式参数中获取数据库信息
  const database = explicitDatabase ||
                  extractDatabaseFromPath(url || '') ||
                  undefined;

  // 安全处理URL解析
  let pathname = '/';
  let searchParams = undefined;

  try {
    if (url) {
      const urlObj = new URL(url);
      pathname = urlObj.pathname;
      searchParams = urlObj.search.slice(1) || undefined;
    }
  } catch (__error) {
    console.warn('Invalid URL in analytics event:', url);
  }

  // 构建活动日志数据
  const logData = {
    userId: eventUserId || defaultData.userId,
    ip: defaultData.ip,
    userAgent: user_agent || defaultData.userAgent,
    path: pathname,
    method: 'BATCH_TRACK',
    queryParams: searchParams,
    referer: referrer || defaultData.referer,
    database,
    eventType,
    sessionId: sessionId || defaultData.sessionId,
    createdAt: timestamp ? new Date(timestamp) : new Date(),
  };

  // 应用数据截断和验证
  return sanitizeData(logData);
}

export async function POST(__request: NextRequest) {
  try {
    const body = await __request.json();
    const { events } = body;

    if (!Array.isArray(events) || events.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Events array is required and must not be empty' },
        { status: 400 }
      );
    }

    // 限制批量大小
    if (events.length > 100) {
      return NextResponse.json(
        { success: false, error: 'Too many events in batch (max 100)' },
        { status: 400 }
      );
    }

    // 获取用户会话信息
    const session = await getSession();
    const userId = session?.userId || null;

    // 获取请求信息
    const ip = getClientIP(__request);
    const userAgent = __request.headers.get('user-agent') || undefined;
    const referer = __request.headers.get('referer') || undefined;

    const defaultData = {
      userId,
      ip,
      userAgent,
      referer,
      sessionId: null, // 将从事件中获取
    };

    // 处理所有事件
    const processedEvents = [];
    const errors = [];

    for (let i = 0; i < events.length; i++) {
      try {
        const processedEvent = await processEvent(events[i], defaultData);
        if (processedEvent) {
          processedEvents.push(processedEvent);
        }
      } catch (__error) {
        errors.push({
          index: i,
          error: __error instanceof Error ? __error.message : 'Unknown error'
        });
      }
    }

    // 批量插入到数据库
    if (processedEvents.length > 0) {
      try {
        await db.activityLog.createMany({
          data: processedEvents,
          skipDuplicates: true, // 跳过重复记录
        });
      } catch (_dbError) {
        console.error('Database batch insert error:', _dbError);
        return NextResponse.json(
          { success: false, error: 'Failed to save events to database' },
          { status: 500 }
        );
      }
    }

    // 处理特定事件类型的额外逻辑
    for (const event of processedEvents) {
      try {
        await handleSpecialEvents(event);
      } catch (__error) {
        console.warn('Special event handling failed:', __error);
        // 不阻塞主流程
      }
    }

    const response = {
      success: true,
      message: 'Events tracked successfully',
      processed: processedEvents.length,
      errors: errors.length > 0 ? errors : undefined,
    };

    return NextResponse.json(response);

  } catch (__error) {
    console.error('Analytics batch tracking error:', __error);
    return NextResponse.json(
      { success: false, error: 'Failed to process batch events' },
      { status: 500 }
    );
  }
}

// 处理特定事件类型的额外逻辑
async function handleSpecialEvents(eventData: ActivityLogData): Promise<void> {
  switch (eventData.eventType) {
    case 'error_occurred':
      // 可以发送错误报告到监控系统
      await handleErrorEvent(eventData);
      break;

    case 'performance_metric':
      // 可以进行性能分析
      await handlePerformanceEvent(eventData);
      break;

    case 'user_login':
    case 'user_logout':
      // 可以更新用户会话状态
      await handleUserSessionEvent(eventData);
      break;

    case 'database_search':
    case 'advanced_search':
      // 可以更新搜索统计
      await handleSearchEvent(eventData);
      break;

    default:
      // 其他事件的通用处理
      break;
  }
}

// 处理错误事件
async function handleErrorEvent(eventData: ActivityLogData): Promise<void> {
  // 这里可以集成错误监控服务，如Sentry
  console.warn('Error event tracked:', {
    eventType: eventData.eventType,
    path: eventData.path,
    userId: eventData.userId,
    timestamp: eventData.createdAt,
  });
}

// 处理性能事件
async function handlePerformanceEvent(eventData: ActivityLogData): Promise<void> {
  // 这里可以进行性能分析和报警
  console.error('Performance event tracked:', {
    eventType: eventData.eventType,
    path: eventData.path,
    timestamp: eventData.createdAt,
  });
}

// 处理用户会话事件
async function handleUserSessionEvent(eventData: ActivityLogData): Promise<void> {
  // 这里可以更新用户会话状态
  console.error('User session event tracked:', {
    eventType: eventData.eventType,
    userId: eventData.userId,
    timestamp: eventData.createdAt,
  });
}

// 处理搜索事件
async function handleSearchEvent(eventData: ActivityLogData): Promise<void> {
  // 这里可以更新搜索统计和热门搜索词
  console.error('Search event tracked:', {
    eventType: eventData.eventType,
    database: eventData.database,
    timestamp: eventData.createdAt,
  });
}

// 支持 GET 请求用于健康检查
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Analytics batch tracking API is running',
    endpoints: {
      batchTrack: 'POST /api/analytics/batch-track'
    },
    limits: {
      maxBatchSize: 100,
      maxEventSize: '10KB'
    }
  });
}
